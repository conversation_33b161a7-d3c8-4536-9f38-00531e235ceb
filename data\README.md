# Games Data System - upfast.online

## Overview
This system provides a dynamic way to manage and display games on the upfast.online website. Instead of hardcoding games in HTML, all game data is stored in JSON files and loaded dynamically.

## Files Structure

### 1. `data/games.json`
Contains all 75 games with their details:
- Game ID, name, and description
- Currency name and amounts
- Game icons and background images
- Content locker links
- Categories

### 2. `data/site-settings.json`
Contains website configuration:
- Site information and branding
- SEO settings
- UI configuration
- Security settings
- Categories definition

### 3. `js/games-loader.js`
JavaScript class that:
- Loads games from JSON
- Renders games dynamically
- Handles search and filtering
- Manages game generation modal

### 4. `css/games-style.css`
Additional CSS for:
- Game grid enhancements
- Search and filter controls
- Modal styling
- Responsive design

## Features

### Dynamic Game Loading
- All 75 games are loaded from JSON
- No need to edit HTML for new games
- Automatic grid layout

### Search & Filter
- Real-time search by game name, currency, or description
- Category-based filtering
- Responsive design

### Game Generation Modal
- Interactive modal for each game
- Currency amount selection
- Username input
- Content locker integration

### Categories
- Battle Royale
- Sports
- Strategy
- Puzzle
- Simulation
- Action
- Social
- Casino
- RPG
- Sandbox

## How to Add New Games

1. Open `data/games.json`
2. Add new game object to the "games" array:

```json
{
  "id": "new_game",
  "name": "New Game",
  "currency_name": "Coins",
  "currency_amounts": ["1000", "2500", "5000", "10000"],
  "game_icon": "img/game-icons/newgame.webp",
  "background_image": "img/background-images/newgame-bg.jpg",
  "currency_icon": "img/currency-icons/coins.png",
  "content_locker_links": [
    "https://fastmod.online/goo/link1",
    "https://fastmod.online/goo/link2"
  ],
  "description": "Generate free Coins for New Game",
  "category": "action"
}
```

## How to Modify Site Settings

Edit `data/site-settings.json` to change:
- Site name and description
- Social media links
- SEO settings
- UI colors and layout
- Security settings

## Image Requirements

### Game Icons
- Path: `img/game-icons/`
- Format: WebP recommended
- Size: 100x100px minimum
- Naming: `gamename.webp`

### Background Images
- Path: `img/background-images/`
- Format: JPG recommended
- Size: 1920x1080px recommended
- Naming: `gamename-bg.jpg`

### Currency Icons
- Path: `img/currency-icons/`
- Format: PNG recommended
- Size: 64x64px
- Naming: `currencyname.png`

## Content Locker Integration

Each game can have multiple content locker links. The system randomly selects one when a user generates currency. Links should be in this format:
```
https://fastmod.online/goo/XXXXXX
```

## Browser Compatibility

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile responsive
- ES6+ JavaScript features used

## Performance

- Lazy loading of images
- Efficient filtering and search
- Minimal DOM manipulation
- CSS animations for smooth UX

## Security Features

- Input validation
- Rate limiting (configurable)
- Content protection options
- Safe JSON parsing

## Customization

### Colors
Edit the theme section in `site-settings.json`:
```json
"theme": {
  "primary_color": "#007bff",
  "secondary_color": "#6c757d",
  ...
}
```

### Layout
Modify the layout section:
```json
"layout": {
  "show_header": true,
  "show_footer": true,
  "enable_animations": true,
  ...
}
```

### Games Display
Configure games display:
```json
"games_display": {
  "games_per_page": 12,
  "show_categories": true,
  "enable_search": true,
  ...
}
```

## Troubleshooting

### Games Not Loading
1. Check browser console for errors
2. Verify JSON syntax in `games.json`
3. Ensure all image paths are correct
4. Check network requests in browser dev tools

### Search Not Working
1. Verify `gameSearch` input element exists
2. Check JavaScript console for errors
3. Ensure `games-loader.js` is loaded

### Modal Issues
1. Check CSS file is loaded
2. Verify modal HTML structure
3. Check for JavaScript conflicts

## Future Enhancements

- Admin panel for game management
- Analytics integration
- A/B testing for content lockers
- Multi-language support
- Game popularity tracking

## Support

For technical support or questions about this system, contact the development team or refer to the main website documentation.
