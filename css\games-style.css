/**
 * Games Style - Additional styles for games display and modal
 * Author: upfast.online
 */

/* Games Grid Enhancements */
.tweaked-apps-grid-item {
    transition: all 0.3s ease;
    cursor: pointer;
}

.tweaked-apps-grid-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.game-amounts {
    font-size: 0.8em;
    color: #666;
    margin: 5px 0;
    text-align: center;
}

.game-icon {
    transition: transform 0.3s ease;
}

.tweaked-apps-grid-item:hover .game-icon {
    transform: scale(1.1);
}

/* Search and Filter Section */
.games-controls {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-container {
    position: relative;
    max-width: 400px;
    margin: 0 auto 20px;
}

#gameSearch {
    width: 100%;
    padding: 12px 20px 12px 45px;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

#gameSearch:focus {
    outline: none;
    border-color: #007bff;
}

.search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.category-filters {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
}

.category-btn {
    padding: 8px 16px;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
}

.category-btn:hover,
.category-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

/* Game Modal Styles */
.game-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.game-modal {
    background: white;
    border-radius: 15px;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideInUp 0.3s ease;
}

.game-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 15px 15px 0 0;
}

.game-modal-header h2 {
    margin: 0;
    font-size: 1.5em;
}

.close-modal {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.close-modal:hover {
    background: rgba(255, 255, 255, 0.2);
}

.game-modal-body {
    padding: 20px;
}

.game-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.modal-game-icon {
    width: 80px;
    height: 80px;
    border-radius: 15px;
    margin-right: 15px;
    object-fit: cover;
}

.game-details h3 {
    margin: 0 0 10px 0;
    color: #333;
}

.game-details p {
    margin: 5px 0;
    color: #666;
    line-height: 1.4;
}

.currency-selection {
    margin-bottom: 20px;
}

.currency-selection h4 {
    margin-bottom: 15px;
    color: #333;
}

.currency-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
}

.currency-btn {
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    text-align: center;
}

.currency-btn:hover {
    border-color: #007bff;
    background: #f8f9ff;
}

.currency-btn.selected {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.generator-form {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
}

#userInput {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    margin-bottom: 15px;
    transition: border-color 0.3s ease;
}

#userInput:focus {
    outline: none;
    border-color: #007bff;
}

.generate-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.generate-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #218838, #1e7e34);
    transform: translateY(-2px);
}

.generate-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.success-message {
    text-align: center;
    padding: 40px 20px;
}

.success-icon {
    font-size: 4em;
    margin-bottom: 20px;
}

.success-message h3 {
    color: #28a745;
    margin-bottom: 15px;
}

.success-message p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 15px;
}

.close-btn {
    padding: 12px 30px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: #0056b3;
}

/* Error Message */
.error-message {
    text-align: center;
    padding: 40px 20px;
    color: #dc3545;
}

.error-message h3 {
    margin-bottom: 15px;
}

.error-message button {
    padding: 10px 20px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 15px;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animated {
    animation-duration: 0.6s;
    animation-fill-mode: both;
}

.fadeInUp {
    animation-name: fadeInUp;
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-modal {
        width: 95%;
        margin: 10px;
    }
    
    .game-info {
        flex-direction: column;
        text-align: center;
    }
    
    .modal-game-icon {
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .currency-options {
        grid-template-columns: 1fr;
    }
    
    .category-filters {
        justify-content: center;
    }
    
    .category-btn {
        font-size: 12px;
        padding: 6px 12px;
    }
}
