<!--


It is forbidden to re-sell this landing page without Author Permission.

 -->
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en">
    
<head>
        <title>upfast.online</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
        <meta name="description" content="Download Unlock Apps for Android and iOS"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
        <link rel="icon" type="image/ico" href="img/site-icons/favicon.png"/>
        <!-- Open Graph Meta Tags-->
        <meta property="og:title" content="Sawan - Unlock Apps Installer"/>
        <!-- Title which is displayed when your site is shared on social networks -->
        <meta property="og:description" content="Download Tweaked Apps for Android and iOS"/>
        <!-- Website description which is displayed when your site is shared on social networks -->
        <meta property="og:type" content="website"/>
        <meta property="og:url" content="../www.downhack.com/index.html"/>
        <!-- Your Website URL -->
        <meta property="og:image" content="../static.hugedomains.com/images/logo_huge_domains.gif"/>
        <!-- Absolute Path to the Image which will display, when your website is shared on social networks -->
        <!-- Twitter Meta -->
        <meta name="twitter:card" content="summary"/>
        <meta name="twitter:site" content="@tweetname"/>
        <meta name="twitter:title" content="Downhack - xtik.online"/>
        <meta name="twitter:description" content="Download Unlocked Apps for Android and iOS"/>
        <meta name="twitter:image" content="../static.hugedomains.com/images/logo_huge_domains.gif"/>
        <!-- Icons -->
        <link rel="stylesheet" href="../cdn.linearicons.com/free/1.0.0/icon-font.min.css">
        <link rel="stylesheet" href="../use.fontawesome.com/releases/v5.5.0/css/all.css" integrity="sha384-B4dIYHKNBt8Bc12p+WXckhzcICo0wtJAoU8YZTY5qE0Id1GSseTk6S+L3BlXeVIU" crossorigin="anonymous">
        <!-- Google Fonts -->
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
        <!-- CSS -->
        <link href="css/bootstrap.min.css" rel="stylesheet"/>
        <link href="css/animate.css" rel="stylesheet"/>
        <link href="css/style.css" rel="stylesheet"/>
        <link href="css/games-style.css" rel="stylesheet"/>
    </head>
    <body>
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-6">
                    <div class="left-side-wrapper">
                        <header>
                            <img src="img/site-icons/Logo.png" class="img-fluid app-instal-icon animated bounceIn"/>
                            <div class="h-intro">
                                <h1 class="animated bounceIn animation-delay-200"><span>upfast.online</span> </h1>
                                <p class="animated bounceIn animation-delay-400">Generating Currencies for Android and iOS.</p>
                            </div>
                        </header>
                        <div class="search-section animated bounceIn animation-delay-600">
                            <div class="search-content">
                                <h3>Find your app</h3>
                                <div class="input-icon-wrapper">
                                    <i class="fas fa-search"></i>
                                    <input type="text" class="quicksearch input-style" placeholder="Search for apps..."/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="right-side-wrapper">
                        <section class="tweaked-apps-section animated fadeIn animation-delay-800">
                            <div id="app-particles"></div>
                            <div class="tweaked-apps-header">
                                <h2>The First Site in Generating Currencies</h2>
                            </div>

                            <!-- Games Controls Section -->
                            <div class="games-controls">
                                <div class="search-container">
                                    <i class="fas fa-search search-icon"></i>
                                    <input type="text" id="gameSearch" placeholder="Search games...">
                                </div>
                                <div class="category-filters">
                                    <button class="category-btn active" data-category="all">All Games</button>
                                    <button class="category-btn" data-category="battle_royale">Battle Royale</button>
                                    <button class="category-btn" data-category="sports">Sports</button>
                                    <button class="category-btn" data-category="strategy">Strategy</button>
                                    <button class="category-btn" data-category="puzzle">Puzzle</button>
                                    <button class="category-btn" data-category="simulation">Simulation</button>
                                    <button class="category-btn" data-category="action">Action</button>
                                    <button class="category-btn" data-category="social">Social</button>
                                    <button class="category-btn" data-category="casino">Casino</button>
                                </div>
                            </div>

                            <div class="tweaked-apps-content">
                                <!-- Games will be loaded dynamically from data/games.json via JavaScript -->
                                <div class="loading-message" style="text-align: center; padding: 40px;">
                                    <i class="fas fa-spinner fa-spin" style="font-size: 2em; color: #007bff;"></i>
                                    <p style="margin-top: 15px; color: #666;">Loading games...</p>
                                </div>
                            </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    <!-- JS -->
    <script type="text/javascript" src="5pro/jquery.min.js"></script>
    <script type="text/javascript" src="5pro/isotope.pkgd.min.js"></script>
    <script type="text/javascript" src="5pro/particles.min.js"></script>
    <script type="text/javascript" src="5pro/main.js"></script>
    <script type="text/javascript" src="js/games-loader.js"></script>


</html>
