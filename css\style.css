/**************************



It is forbidden to re-sell this landing page without Author Permission.

**************************/
/*--------------------------------------------------------------
# Basics
--------------------------------------------------------------*/
body {
	overflow-x: hidden;
	background: #f1f1f1;
}
html {
	overflow-y: scroll;
	overflow-x: hidden;
}
ul {
	list-style: none;
}
textarea, input, a, button { 
	outline: none;
}
/*--------------------------------------------------------------
# Typography
--------------------------------------------------------------*/
body {
	font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
	font-size: 16px;
	color: #fff;
	line-height: 1.6;
	font-weight: 400;
}
a {
	-webkit-transition: all 0.2s ease-in-out 0s;
	-moz-transition: all 0.2s ease-in-out 0s;
	-ms-transition: all 0.2s ease-in-out 0s;
	-o-transition: all 0.2s ease-in-out 0s;
	transition: all 0.2s ease-in-out 0s;
}
a:hover, a:visited, a:focus, a:active, button:hover, button:visited, button:active, button:focus {
	text-decoration: none !important;
	outline: none !important;
}
::selection {
	background: #000;
	color: #fff;
	text-shadow: none;
}
::-moz-selection {
	background: #000;
	color: #fff;
	text-shadow: none;
}

/*--------------------------------------------------------------
# Animations
--------------------------------------------------------------*/
.animation-delay-100 {
	-webkit-animation-delay: 0.1s;
	animation-delay: 0.1s;
}
.animation-delay-200 {
	-webkit-animation-delay: 0.2s;
	animation-delay: 0.2s;
}
.animation-delay-300 {
	-webkit-animation-delay: 0.3s;
	animation-delay: 0.3s;
}
.animation-delay-400 {
	-webkit-animation-delay: 0.4s;
	animation-delay: 0.4s;
}
.animation-delay-600 {
	-webkit-animation-delay: 0.6s;
	animation-delay: 0.6s;
}
.animation-delay-800 {
	-webkit-animation-delay: 0.8s;
	animation-delay: 0.8s;
}
.animation-delay-1000 {
	-webkit-animation-delay: 1s;
	animation-delay: 1s;
}
.animation-delay-2000 {
	-webkit-animation-delay: 2s;
	animation-delay: 2s;
}

/* Enhanced animations */
@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(30px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes slideInLeft {
	from {
		opacity: 0;
		transform: translateX(-50px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes pulse {
	0%, 100% {
		transform: scale(1);
	}
	50% {
		transform: scale(1.05);
	}
}

.tweaked-apps-grid-item {
	animation: fadeInUp 0.6s ease-out;
}

.tweaked-apps-grid-item:nth-child(1) { animation-delay: 0.1s; }
.tweaked-apps-grid-item:nth-child(2) { animation-delay: 0.2s; }
.tweaked-apps-grid-item:nth-child(3) { animation-delay: 0.3s; }
.tweaked-apps-grid-item:nth-child(4) { animation-delay: 0.4s; }
.tweaked-apps-grid-item:nth-child(5) { animation-delay: 0.5s; }
.tweaked-apps-grid-item:nth-child(6) { animation-delay: 0.6s; }

/*--------------------------------------------------------------
# Left Side
--------------------------------------------------------------*/
.left-side-wrapper {
	position: fixed;
	min-height: 100vh;
	left: 0;
	top: 0;
	width: 50%;
	padding: 100px 100px 70px 100px;
}
.left-side-wrapper:before {
	content: '';
	display: block;
	position: absolute;
	left: 15px;
	top: 0;
	height: 100%;
	width: 100%;
	background: #4c1d95;
	z-index: -2;
	clip-path: polygon(0% 0%, 100% 0%, 92% 100%, 0 100%);
}
.left-side-wrapper:after {
	content: '';
	display: block;
	position: absolute;
	left: 0;
	top: 0;
	height: 100%;
	width: 100%;
	background: #6d28d9;
	z-index: -1;
	clip-path: polygon(0% 0%, 100% 0%, 92% 100%, 0 100%);
}
header {
	position: relative;
	text-align: center;
}
.app-instal-icon {
	display: table;
	margin: 0 auto 20px auto;
	max-width: 150px;
}
.h-intro {
	position: relative;
}
.h-intro h1 {
	font-size: 4.5em;
	color: #fff;
	text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
	line-height: 1.1;
	margin: 0 0 25px 0;
	font-weight: 800;
	letter-spacing: -1px;
}
.h-intro h1 span {
	font-weight: 300;
	display: block;
	font-size: 0.9em;
	margin-top: 5px;
}
.h-intro p {
	font-size: 1.4em;
	max-width: 600px;
	margin: 0 auto;
	color: rgba(255, 255, 255, 0.9);
	font-weight: 400;
	letter-spacing: 1px;
	line-height: 1.5;
}
.search-section {
	position: relative;
	margin: 60px auto 0 auto;
}
.search-content {
	position: relative;
	max-width: 550px;
	margin: 0 auto;
}
.search-content h3 {
	font-size: 1.3em;
	letter-spacing: 1px;
	text-transform: uppercase;
	text-align: center;
	margin: 0 0 20px 0;
	color: rgba(255, 255, 255, 0.9);
	font-weight: 600;
}
.input-icon-wrapper {
	position: relative;
	margin: 0 auto;
}
.input-icon-wrapper i {
	position: absolute;
	color: rgba(255, 255, 255, 0.7);
	left: 25px;
	top: 50%;
	transform: translateY(-50%);
	font-size: 20px;
	z-index: 2;
}
.input-style {
	height: 65px;
	font-size: 1.1em;
	color: #fff;
	outline: none !important;
	width: 100%;
	padding: 0 25px 0 60px;
	border: 2px solid rgba(255, 255, 255, 0.2);
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
	background: rgba(255, 255, 255, 0.1);
	border-radius: 50px;
	backdrop-filter: blur(10px);
	transition: all 0.3s ease;
}
.input-style:focus {
	border-color: rgba(255, 255, 255, 0.4);
	background: rgba(255, 255, 255, 0.15);
	box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
	transform: translateY(-2px);
}
.input-style::-webkit-input-placeholder { /* Chrome/Opera/Safari */
	color: #fff;
	opacity: 1;
}
.input-style::-moz-placeholder { /* Firefox 19+ */
	color: #fff;
	opacity: 1;
}
.input-style:-ms-input-placeholder { /* IE 10+ */
	color: #fff;
	opacity: 1;
}
.input-style:-moz-placeholder { /* Firefox 18- */
	color: #fff;
	opacity: 1;
}

/*--------------------------------------------------------------
# Right Side
--------------------------------------------------------------*/
.right-side-wrapper {
	position: relative;
	min-height: 100vh;
	padding: 100px 50px 70px 50px;
}
#app-particles {
	position: fixed;
	width: 60%;
	height: 100vh;
	right: 0;
	top: 0;
	z-index: -3;
}
.tweaked-apps-section {
	position: relative;
}
.tweaked-apps-header {
	position: relative;
	z-index: 10;
	margin: 0 0 50px 0;
}
.tweaked-apps-header h2 {
	font-size: 2.8em;
	text-align: center;
	color: #1f2937;
	font-weight: 700;
	line-height: 1.2;
	margin-bottom: 15px;
}
.tweaked-apps-header h2:after {
	content: '';
	display: block;
	width: 80px;
	height: 4px;
	background: linear-gradient(90deg, #6d28d9, #a855f7);
	margin: 20px auto 0;
	border-radius: 2px;
}
.tweaked-apps-content {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	gap: 20px;
	align-items: stretch;
}

/* Mobile-specific adjustments for 2 cards per row */
@media screen and (max-width: 550px) {
	.tweaked-apps-content {
		gap: 15px;
		justify-content: center;
		padding: 0 10px;
		align-items: stretch;
	}
}
.tweaked-apps-grid-item {
	padding: 8px;
	width: calc(33.333% - 16px);
	min-width: 167.5px;
	max-width: 167.5px;
	flex: 0 0 calc(33.333% - 16px);
	display: flex;
	flex-direction: column;
}
.tweaked-apps-grid-item-background {
	position: relative;
	border-radius: 16px;
	overflow: hidden;
	background: #ffffff;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	border: 1px solid rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
	height: 203px;
	width: 167.5px;
	display: flex;
	flex-direction: column;
}
.tweaked-apps-grid-item-background:hover {
	transform: translateY(-8px) scale(1.02);
	box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
	border-color: rgba(109, 40, 217, 0.2);
}
.tweaked-apps-grid-item-content {
	position: relative;
	padding: 12px 8px;
	text-align: center;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: space-between;
	height: 100%;
	flex-grow: 1;
}
/* New Game Card Structure */
.game-icon-container {
	width: 70px;
	height: 70px;
	border-radius: 50%;
	margin: 0 auto 12px auto;
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #4a00e0, #8e2de2);
	box-shadow: 0 4px 12px rgba(138, 43, 226, 0.3);
	transition: all 0.3s ease;
	overflow: hidden;
}
.game-icon {
	width: 70px;
	height: 70px;
	border-radius: 50%;
	object-fit: cover;
	transition: transform 0.3s ease;
}

.tweaked-apps-grid-item-background:hover .game-icon-container {
	transform: scale(1.05);
	box-shadow: 0 6px 16px rgba(138, 43, 226, 0.4);
}

.tweaked-apps-grid-item-background:hover .game-icon {
	transform: scale(1.1);
}
.game-name {
	font-size: 14px;
	font-weight: 600;
	color: #333;
	margin-bottom: 8px;
	text-align: center;
	line-height: 1.2;
	transition: color 0.3s ease;
}
.game-currency {
	font-size: 12px;
	color: #666;
	background-color: rgba(138, 43, 226, 0.1);
	padding: 4px 12px;
	border-radius: 12px;
	margin-bottom: 12px;
	font-weight: 500;
	transition: all 0.3s ease;
	text-align: center;
}
.game-button {
	margin-top: auto;
	width: 100%;
}

.game-button a {
	display: inline-block;
	background: linear-gradient(90deg, #ff416c, #ff4b2b);
	color: white;
	padding: 8px 20px;
	border-radius: 20px;
	text-decoration: none;
	font-weight: 600;
	font-size: 12px;
	text-transform: uppercase;
	letter-spacing: 0.5px;
	transition: all 0.3s ease;
	box-shadow: 0 3px 8px rgba(255, 65, 108, 0.3);
	width: 90%;
	text-align: center;
}
/* Hover Effects */
.tweaked-apps-grid-item-background:hover .game-name {
	color: #4a00e0;
}

.tweaked-apps-grid-item-background:hover .game-currency {
	background-color: rgba(138, 43, 226, 0.15);
	color: #4a00e0;
}

.game-button a:hover {
	background: linear-gradient(90deg, #ff4b2b, #ff416c);
	box-shadow: 0 5px 12px rgba(255, 65, 108, 0.4);
	transform: translateY(-1px);
}
.tweaked-apps-grid-item:nth-child(3) .tweaked-app-title:after {
	content: "Diamonds";
	display: block;
	color: #9ca3af;
	font-size: 0.75em;
	font-weight: 500;
	margin-top: 5px;
	text-transform: none;
	letter-spacing: 0.5px;
}
.tweaked-apps-grid-item:nth-child(4) .tweaked-app-title:after {
	content: "Robux";
	display: block;
	color: #9ca3af;
	font-size: 0.75em;
	font-weight: 500;
	margin-top: 5px;
	text-transform: none;
	letter-spacing: 0.5px;
}
.tweaked-apps-grid-item:nth-child(5) .tweaked-app-title:after {
	content: "Coins";
	display: block;
	color: #9ca3af;
	font-size: 0.75em;
	font-weight: 500;
	margin-top: 5px;
	text-transform: none;
	letter-spacing: 0.5px;
}
.tweaked-apps-grid-item:nth-child(6) .tweaked-app-title:after {
	content: "Dice";
	display: block;
	color: #9ca3af;
	font-size: 0.75em;
	font-weight: 500;
	margin-top: 5px;
	text-transform: none;
	letter-spacing: 0.5px;
}
.tweaked-app-subtitle {
	color: #6b7280;
	text-align: center;
	font-size: 0.9em;
	font-weight: 400;
	margin-bottom: 15px;
}
.tweaked-app-button-wrapper {
	position: relative;
	margin: 20px 0 0 0;
	margin-top: auto;
	width: 100%;
}
.tweaked-app-button-wrapper a {
	background: linear-gradient(135deg, #ef4444, #dc2626);
	display: inline-block;
	margin: 0 auto;
	padding: 14px 35px;
	color: #fff;
	border-radius: 50px;
	text-transform: uppercase;
	font-weight: 700;
	font-size: 0.9em;
	letter-spacing: 1px;
	text-decoration: none !important;
	border: none;
	position: relative;
	box-shadow: 0 6px 20px rgba(239, 68, 68, 0.3);
	transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
	overflow: hidden;
}
.tweaked-app-button-wrapper a:before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
	transition: left 0.5s;
}
.tweaked-app-button-wrapper a:hover:before {
	left: 100%;
}
.tweaked-app-button-wrapper a:hover {
	background: linear-gradient(135deg, #dc2626, #b91c1c);
	transform: translateY(-3px);
	box-shadow: 0 12px 35px rgba(239, 68, 68, 0.4);
	color: #fff;
}
.search-error-wrapper {
	display: block;
	text-align: center;
	font-size: 4.2em;
	display: none;
	color: #333;
	margin: 120px 0 0 0;
	position: relative;
	z-index: 10;
	font-weight: 300;
	text-transform: uppercase;
	letter-spacing: 2px;
	z-index: 50;
	min-height: 700px;
}
.search-error-wrapper i {
	font-size: 6em;
	display: block;
	margin: 0 0 20px 0;
	color: #263ebd;
}
.search-error-wrapper p {
	font-size: 0.45em;
	max-width: 650px;
	text-transform: none;
	margin: 20px auto 0 auto;
}

/*--------------------------------------------------------------
# Responsive Design
--------------------------------------------------------------*/
@media screen and (max-width: 1920px) {
	#app-particles {
		width: 55%;
		z-index: -5;
	}
	.left-side-wrapper {
		padding: 80px 80px 70px 80px;
	}
	.right-side-wrapper {
		padding: 50px 40px 70px 40px;
	}
	.app-instal-icon {
		max-width: 200px;
	}
	.h-intro h1 {
		font-size: 4em;
	}
	.h-intro p {
		font-size: 1.2em;
	}
	.tweaked-apps-header {
		margin: 0 0 40px 0;
	}
	.tweaked-apps-header h2 {
		font-size: 2.4em;
	}
	.tweaked-apps-grid-item {
		width: calc(50% - 30px);
		min-width: 280px;
	}
	.tweaked-apps-grid-item-image-wrapper {
		width: 90px;
		height: 90px;
	}
	.tweaked-apps-grid-item-image-wrapper img {
		width: 76px;
		height: 76px;
	}
}
@media screen and (max-width: 1440px) {
	.left-side-wrapper {
		padding: 60px 60px 60px 60px;
	}
	.right-side-wrapper {
		padding: 60px 30px 60px 30px;
	}
	.app-instal-icon {
		max-width: 180px;
	}
	.h-intro h1 {
		font-size: 3.5em;
	}
	.h-intro p {
		font-size: 1.1em;
	}
	.tweaked-apps-grid-item {
		width: calc(50% - 30px);
		min-width: 260px;
	}
	.tweaked-apps-grid-item-image-wrapper {
		width: 85px;
		height: 85px;
	}
	.tweaked-apps-grid-item-image-wrapper img {
		width: 72px;
		height: 72px;
	}
	.tweaked-app-title {
		font-size: 1.2em;
	}
}
@media screen and (max-width: 1280px) {
	.left-side-wrapper {
		padding: 50px 60px 50px 60px;
	}
	.right-side-wrapper {
		padding: 70px 0 70px 0;
	}
	.app-instal-icon {
		max-width: 220px;
	}
	.h-intro h1 {
		font-size: 3.8em;
	}
	.h-intro p {
		font-size: 1.6em;
	}
	.right-side-wrapper {
		padding: 40px 0 70px 0;
	}
	.tweaked-apps-header h2 {
		font-size: 2.4em;
	}	
	.tweaked-apps-grid-item img {
		max-width: 80px;
	}
	.tweaked-app-title {
		font-size: 1.1em;
	}
	.input-style {
		height: 100px;
	}
	.input-icon-wrapper i {
		top: 30px;
	}
}
@media screen and (max-width: 993px) {
	#app-particles {
		width: 100%;
	}
	.container-fluid {
		padding: 0;
	}
	.left-side-wrapper {
		position: relative;
		min-height: auto;
		width: 100%;
		padding: 50px 40px 60px 40px;
		z-index: 60000;
	}
	.h-intro h1 {
		font-size: 3em;
	}
	.h-intro p {
		font-size: 1em;
	}
	.input-style {
		height: 55px;
		font-size: 1em;
	}
	.left-side-wrapper:before {
		content: '';
		display: block;
		position: absolute;
		left: 0;
		top: 15px;
		height: 100%;
		width: 100%;
		background: #4c1d95;
		z-index: -2;
		clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 0% 94%);
	}
	.left-side-wrapper:after {
		content: '';
		display: block;
		position: absolute;
		left: 0;
		top: 0;
		height: 100%;
		width: 100%;
		background: #6d28d9;
		z-index: -1;
		clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 0% 94%);
	}
	.right-side-wrapper {
		padding: 40px 80px 70px 80px;
	}
	.app-instal-icon {
		max-width: 200px;
	}
	.search-section {
		position: relative;
		margin: 25px auto 0 auto;
	}
}
@media screen and (max-width: 767px) {
	.app-instal-icon {
		max-width: 170px;
	}
	.right-side-wrapper {
		padding: 40px 50px 70px 50px;
	}
	.search-error-wrapper {
		font-size: 2em;
		margin-top: 30px;
	}
	.search-error-wrapper p {
		font-size: 0.7em;
	}
}
/* Desktop and Large Screens - 3 cards per row */
@media screen and (min-width: 769px) {
	.tweaked-apps-content {
		max-width: 600px;
		margin: 0 auto;
		justify-content: space-between;
	}

	.tweaked-apps-grid-item {
		width: calc(33.333% - 14px);
		flex: 0 0 calc(33.333% - 14px);
		max-width: 167.5px;
		min-width: 167.5px;
	}
}

/* Tablet and Medium Screens - 2 cards per row */
@media screen and (max-width: 768px) and (min-width: 551px) {
	.tweaked-apps-content {
		max-width: 400px;
		margin: 0 auto;
		justify-content: space-between;
	}

	.tweaked-apps-grid-item {
		width: calc(50% - 12px);
		flex: 0 0 calc(50% - 12px);
		max-width: 167.5px;
		min-width: 167.5px;
	}
}

/* Mobile Responsive Design */
@media screen and (max-width: 550px) {
	.tweaked-apps-header h2 {
		font-size: 1.8em;
		margin-bottom: 30px;
	}

	.tweaked-apps-content {
		max-width: 100%;
		width: 100%;
		margin: 0 auto;
		gap: 10px;
		justify-content: space-evenly;
		padding: 0 10px;
	}

	.tweaked-apps-grid-item {
		width: calc(50% - 10px);
		max-width: 160px;
		min-width: 140px;
		padding: 5px;
		flex: 0 0 calc(50% - 10px);
	}

	.tweaked-apps-grid-item-background {
		height: 190px;
		width: 100%;
	}

	.tweaked-apps-grid-item-content {
		padding: 10px 6px;
	}

	.game-icon-container {
		width: 70px;
		height: 70px;
		margin-bottom: 8px;
	}

	.game-icon {
		width: 70px;
		height: 70px;
	}

	.game-name {
		font-size: 13px;
		margin-bottom: 6px;
	}

	.game-currency {
		font-size: 11px;
		padding: 3px 8px;
		margin-bottom: 8px;
	}

	.game-button a {
		padding: 6px 16px;
		font-size: 11px;
		width: 85%;
	}
}
@media screen and (max-width: 450px) {
	.left-side-wrapper {
		padding: 30px 25px 60px 25px;
	}
	.app-instal-icon {
		max-width: 150px;
	}
	.h-intro h1 {
		font-size: 3em;
	}
	.h-intro p {
		font-size: 1.4em;
	}
	.right-side-wrapper {
		padding: 40px 20px 70px 20px;
	}
	.search-content h3 {
		font-size: 1.2em;
	}
	.tweaked-apps-header h2 {
		font-size: 1.8em;
	}
}
@media screen and (max-width: 400px) {
	.left-side-wrapper {
		padding: 15px 25px 50px 25px;
	}
	.app-instal-icon {
		max-width: 100px;
		margin: 0 auto 5px auto;
	}
	.h-intro h1 {
		font-size: 2.6em;
		margin-bottom: 10px;
	}
	.h-intro p {
		font-size: 1.2em;
		letter-spacing: 2px;
	}
	.input-style {
		height: 80px;
	}
	.input-icon-wrapper i {
		top: 20px;
	}
	.right-side-wrapper {
		padding: 30px 20px 50px 20px;
	}
	.tweaked-apps-header {
		margin: 0 0 0 0;
	}
	.tweaked-apps-header h2 {
		margin: 0;
		font-size: 1.6em;
	}
	.search-error-wrapper {
		font-size: 1.6em;
		margin-top: 10px;
	}
	.search-error-wrapper i {
		margin: 0;
	}
	.search-error-wrapper p {
		margin: 0;
	}
}
@media screen and (max-width: 350px) {
	.app-instal-icon {
		max-width: 120px;
		margin: 0 auto 10px auto;
	}
	.h-intro h1 {
		font-size: 2.2em;
		margin-bottom: 15px;
	}
	.h-intro p {
		font-size: 0.85em;
		letter-spacing: 0.5px;
	}
	.input-style {
		height: 50px;
		padding-left: 50px;
		font-size: 0.9em;
	}
	.input-icon-wrapper i {
		left: 18px;
		font-size: 16px;
	}
	.right-side-wrapper {
		padding: 30px 15px 50px 15px;
	}
	.tweaked-apps-content {
		max-width: 100%;
		width: 100%;
		gap: 8px;
		padding: 0 8px;
		justify-content: space-evenly;
	}

	.tweaked-apps-grid-item {
		width: calc(50% - 8px);
		max-width: 150px;
		min-width: 130px;
		padding: 4px;
		flex: 0 0 calc(50% - 8px);
	}

	.tweaked-apps-grid-item-background {
		height: 180px;
		width: 100%;
	}

	.tweaked-apps-grid-item-content {
		padding: 8px 5px;
	}

	.game-icon-container {
		width: 60px;
		height: 60px;
		margin-bottom: 6px;
	}

	.game-icon {
		width: 60px;
		height: 60px;
	}

	.game-name {
		font-size: 12px;
		margin-bottom: 5px;
		line-height: 1.1;
	}

	.game-currency {
		font-size: 10px;
		padding: 2px 6px;
		margin-bottom: 6px;
	}

	.game-button a {
		padding: 5px 12px;
		font-size: 10px;
		width: 80%;
	}
}

/* Extra Small Mobile Screens */
@media screen and (max-width: 320px) {
	.tweaked-apps-content {
		max-width: 100%;
		width: 100%;
		gap: 6px;
		padding: 0 6px;
		justify-content: space-evenly;
	}

	.tweaked-apps-grid-item {
		width: calc(50% - 6px);
		max-width: 140px;
		min-width: 120px;
		padding: 3px;
		flex: 0 0 calc(50% - 6px);
	}

	.tweaked-apps-grid-item-background {
		height: 170px;
		width: 100%;
	}

	.game-icon-container {
		width: 50px;
		height: 50px;
		margin-bottom: 5px;
	}

	.game-icon {
		width: 50px;
		height: 50px;
	}

	.game-name {
		font-size: 11px;
		margin-bottom: 4px;
	}

	.game-currency {
		font-size: 9px;
		padding: 2px 5px;
		margin-bottom: 5px;
	}

	.game-button a {
		padding: 4px 10px;
		font-size: 9px;
		width: 75%;
	}
}
/**************************


/
It is forbidden to re-sell this landing page without Author Permission.

**************************/