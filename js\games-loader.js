/**
 * Games Loader - Dynamically loads and displays games from JSON data
 * Author: upfast.online
 */

class GamesLoader {
    constructor() {
        this.games = [];
        this.siteSettings = {};
        this.currentCategory = 'all';
        this.searchQuery = '';
        this.init();
    }

    async init() {
        try {
            await this.loadData();
            this.renderGames();
            this.setupEventListeners();
        } catch (error) {
            console.error('Error initializing games loader:', error);
            this.showError('Failed to load games data');
        }
    }

    async loadData() {
        try {
            // Load games data
            const gamesResponse = await fetch('data/games.json');
            if (!gamesResponse.ok) throw new Error('Failed to load games data');
            const gamesData = await gamesResponse.json();
            this.games = gamesData.games;

            // Load site settings
            const settingsResponse = await fetch('data/site-settings.json');
            if (!settingsResponse.ok) throw new Error('Failed to load site settings');
            this.siteSettings = await settingsResponse.json();

        } catch (error) {
            console.error('Error loading data:', error);
            throw error;
        }
    }

    renderGames() {
        const container = document.querySelector('.tweaked-apps-content');
        if (!container) {
            console.error('Games container not found');
            return;
        }

        // Filter games based on category and search
        const filteredGames = this.filterGames();

        // Clear existing content
        container.innerHTML = '';

        // Render filtered games
        filteredGames.forEach(game => {
            const gameElement = this.createGameElement(game);
            container.appendChild(gameElement);
        });

        // Add loading animation
        this.addAnimations();
    }

    filterGames() {
        return this.games.filter(game => {
            // Category filter
            const categoryMatch = this.currentCategory === 'all' || game.category === this.currentCategory;
            
            // Search filter
            const searchMatch = this.searchQuery === '' || 
                game.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                game.currency_name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                game.description.toLowerCase().includes(this.searchQuery.toLowerCase());

            return categoryMatch && searchMatch;
        });
    }

    createGameElement(game) {
        const gameItem = document.createElement('div');
        gameItem.className = 'tweaked-apps-grid-item';
        gameItem.setAttribute('data-category', game.category);
        gameItem.setAttribute('data-game-id', game.id);

        gameItem.innerHTML = `
            <div class="tweaked-apps-grid-item-background">
                <div class="tweaked-apps-grid-item-content">
                    <div class="game-icon-container">
                        <img src="${game.game_icon}" class="game-icon" alt="${game.name}" 
                             onerror="this.src='img/game-icons/default.webp'"/>
                    </div>
                    <div class="game-name">${game.name}</div>
                    <div class="game-currency">${game.currency_name}</div>
                    <div class="game-amounts">${game.currency_amounts.join(' / ')}</div>
                    <div class="game-button">
                        <a href="#" onclick="gamesLoader.openGameGenerator('${game.id}')">Generate</a>
                    </div>
                </div>
            </div>
        `;

        return gameItem;
    }

    openGameGenerator(gameId) {
        const game = this.games.find(g => g.id === gameId);
        if (!game) {
            console.error('Game not found:', gameId);
            return;
        }

        // Create modal or redirect to generator page
        this.showGameModal(game);
    }

    showGameModal(game) {
        // Create modal HTML
        const modalHTML = `
            <div class="game-modal-overlay" id="gameModal">
                <div class="game-modal">
                    <div class="game-modal-header">
                        <h2>${game.name} Generator</h2>
                        <button class="close-modal" onclick="gamesLoader.closeModal()">&times;</button>
                    </div>
                    <div class="game-modal-body">
                        <div class="game-info">
                            <img src="${game.game_icon}" alt="${game.name}" class="modal-game-icon">
                            <div class="game-details">
                                <h3>${game.name}</h3>
                                <p>${game.description}</p>
                                <p><strong>Currency:</strong> ${game.currency_name}</p>
                            </div>
                        </div>
                        <div class="currency-selection">
                            <h4>Select Amount:</h4>
                            <div class="currency-options">
                                ${game.currency_amounts.map(amount => `
                                    <button class="currency-btn" data-amount="${amount}">
                                        ${amount} ${game.currency_name}
                                    </button>
                                `).join('')}
                            </div>
                        </div>
                        <div class="generator-form">
                            <input type="text" placeholder="Enter your username/ID" id="userInput" required>
                            <button class="generate-btn" onclick="gamesLoader.startGeneration('${game.id}')">
                                Start Generation
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Setup currency selection
        this.setupCurrencySelection();
    }

    setupCurrencySelection() {
        const currencyBtns = document.querySelectorAll('.currency-btn');
        currencyBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                currencyBtns.forEach(b => b.classList.remove('selected'));
                btn.classList.add('selected');
            });
        });
    }

    startGeneration(gameId) {
        const userInput = document.getElementById('userInput').value.trim();
        const selectedAmount = document.querySelector('.currency-btn.selected');

        if (!userInput) {
            alert('Please enter your username/ID');
            return;
        }

        if (!selectedAmount) {
            alert('Please select currency amount');
            return;
        }

        const game = this.games.find(g => g.id === gameId);
        const amount = selectedAmount.getAttribute('data-amount');

        // Start generation process
        this.processGeneration(game, userInput, amount);
    }

    processGeneration(game, username, amount) {
        // Show loading state
        const generateBtn = document.querySelector('.generate-btn');
        generateBtn.innerHTML = 'Generating...';
        generateBtn.disabled = true;

        // Simulate generation process
        setTimeout(() => {
            // Redirect to content locker
            if (game.content_locker_links && game.content_locker_links.length > 0) {
                const randomLink = game.content_locker_links[Math.floor(Math.random() * game.content_locker_links.length)];
                window.open(randomLink, '_blank');
            }

            // Show success message
            this.showSuccessMessage(game, amount);
        }, 2000);
    }

    showSuccessMessage(game, amount) {
        const modalBody = document.querySelector('.game-modal-body');
        modalBody.innerHTML = `
            <div class="success-message">
                <div class="success-icon">✅</div>
                <h3>Generation Successful!</h3>
                <p>Your ${amount} ${game.currency_name} for ${game.name} has been generated successfully!</p>
                <p>Please complete the verification process in the new tab to receive your currency.</p>
                <button class="close-btn" onclick="gamesLoader.closeModal()">Close</button>
            </div>
        `;
    }

    closeModal() {
        const modal = document.getElementById('gameModal');
        if (modal) {
            modal.remove();
        }
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('gameSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchQuery = e.target.value;
                this.renderGames();
            });
        }

        // Category filters
        const categoryBtns = document.querySelectorAll('.category-btn');
        categoryBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                categoryBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.currentCategory = btn.getAttribute('data-category');
                this.renderGames();
            });
        });
    }

    addAnimations() {
        const gameItems = document.querySelectorAll('.tweaked-apps-grid-item');
        gameItems.forEach((item, index) => {
            item.style.animationDelay = `${index * 0.1}s`;
            item.classList.add('animated', 'fadeInUp');
        });
    }

    showError(message) {
        const container = document.querySelector('.tweaked-apps-content');
        if (container) {
            container.innerHTML = `
                <div class="error-message">
                    <h3>Error Loading Games</h3>
                    <p>${message}</p>
                    <button onclick="location.reload()">Retry</button>
                </div>
            `;
        }
    }
}

// Initialize games loader when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.gamesLoader = new GamesLoader();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GamesLoader;
}
