/**
 * Games Loader - Dynamically loads and displays games from JSON data
 * Author: upfast.online
 */

class GamesLoader {
    constructor() {
        this.games = [];
        this.init();
    }

    async init() {
        try {
            await this.loadData();
            this.renderGames();
        } catch (error) {
            console.error('Error initializing games loader:', error);
            this.showError('Failed to load games data');
        }
    }

    async loadData() {
        try {
            // Load games data
            const gamesResponse = await fetch('data/games.json');
            if (!gamesResponse.ok) throw new Error('Failed to load games data');
            const gamesData = await gamesResponse.json();
            this.games = gamesData.games;
        } catch (error) {
            console.error('Error loading data:', error);
            throw error;
        }
    }

    renderGames() {
        const container = document.querySelector('.tweaked-apps-content');
        if (!container) {
            console.error('Games container not found');
            return;
        }

        // Clear existing content
        container.innerHTML = '';

        // Render all games with original design
        this.games.forEach(game => {
            const gameElement = this.createGameElement(game);
            container.appendChild(gameElement);
        });
    }

    createGameElement(game) {
        const gameItem = document.createElement('div');
        gameItem.className = 'tweaked-apps-grid-item';

        gameItem.innerHTML = `
            <div class="tweaked-apps-grid-item-background">
                <div class="tweaked-apps-grid-item-content">
                    <div class="game-icon-container">
                        <img src="${game.game_icon}" class="game-icon" alt="${game.name}"
                             onerror="this.src='img/game-icons/default.webp'"/>
                    </div>
                    <div class="game-name">${game.name}</div>
                    <div class="game-currency">${game.currency_name}</div>
                    <div class="game-button">
                        <a href="#" onclick="gamesLoader.openGameGenerator('${game.id}')">start</a>
                    </div>
                </div>
            </div>
        `;

        return gameItem;
    }

    openGameGenerator(gameId) {
        const game = this.games.find(g => g.id === gameId);
        if (!game) {
            console.error('Game not found:', gameId);
            return;
        }

        // Redirect to content locker directly (simple approach)
        if (game.content_locker_links && game.content_locker_links.length > 0) {
            const randomLink = game.content_locker_links[Math.floor(Math.random() * game.content_locker_links.length)];
            window.open(randomLink, '_blank');
        }
    }

    showError(message) {
        const container = document.querySelector('.tweaked-apps-content');
        if (container) {
            container.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #dc3545;">
                    <h3>Error Loading Games</h3>
                    <p>${message}</p>
                    <button onclick="location.reload()" style="padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer;">Retry</button>
                </div>
            `;
        }
    }
}

// Initialize games loader when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.gamesLoader = new GamesLoader();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GamesLoader;
}
